"use client";

import { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';
import { Button } from '@/components/ui/button';

type Testimonial = {
  id: number;
  name: string;
  role: string;
  company: string;
  avatar: string;
  content: string;
  rating: number;
  date: string;
};

export function TestimonialsSection() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const sliderRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: 'أحمد السيد',
      role: 'مدير عام',
      company: 'شركة التقنية المتطورة',
      avatar: '/images/testimonials/avatar-1.jpg',
      content: 'أشكر فريق العمل على الاحترافية العالية في التعامل مع قضيتنا. لقد قدموا لنا استشارات قانونية متميزة ساعدتنا في تجنب العديد من المشاكل القانونية.',
      rating: 5,
      date: '15 مارس 2023'
    },
    {
      id: 2,
      name: 'سارة محمد',
      role: 'رئيسة قسم الشؤون القانونية',
      company: 'مجموعة الأعمال المتحدة',
      avatar: '/images/testimonials/avatar-2.jpg',
      content: 'تعاملنا مع المكتب في عدة قضايا معقدة وكان أداؤهم ممتازاً. ننصح بهم بشدة لكل من يبحث عن استشارات قانونية احترافية.',
      rating: 5,
      date: '2 أبريل 2023'
    },
    {
      id: 3,
      name: 'خالد عبدالله',
      role: 'مالك',
      company: 'مطاعم الذواقة',
      avatar: '/images/testimonials/avatar-3.jpg',
      content: 'المحامي محمد من أفضل من تعاملت معهم في مجال المحاماة. يمتلك خبرة واسعة وأسلوباً مهنياً راقياً في التعامل مع القضايا.',
      rating: 4,
      date: '22 أبريل 2023'
    },
    {
      id: 4,
      name: 'نورة سليمان',
      role: 'مديرة الموارد البشرية',
      company: 'شركة المستقبل',
      avatar: '/images/testimonials/avatar-4.jpg',
      content: 'فريق عمل محترف ويتمتع بأعلى معايير النزاهة والكفاءة. ساعدونا في حل نزاع عمل معقد بكل كفاءة واحترافية.',
      rating: 5,
      date: '5 مايو 2023'
    },
    {
      id: 5,
      name: 'عمر أحمد',
      role: 'مدير مالي',
      company: 'شركة الاستثمارات العقارية',
      avatar: '/images/testimonials/avatar-5.jpg',
      content: 'نحن نتعامل مع المكتب منذ أكثر من 3 سنوات في جميع استشاراتنا القانونية. نثق بهم تماماً وننصح الجميع بالتعامل معهم.',
      rating: 5,
      date: '18 مايو 2023'
    }
  ];

  const visibleTestimonials = testimonials.slice(0, 3); // Show 3 testimonials at a time

  const goToPrev = () => {
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
    resetAutoPlay();
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
    resetAutoPlay();
  };

  const goToSlide = (index: number) => {
    setActiveIndex(index);
    resetAutoPlay();
  };

  const resetAutoPlay = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (isAutoPlaying) {
      intervalRef.current = setInterval(goToNext, 8000);
    }
  };

  useEffect(() => {
    if (isAutoPlaying) {
      intervalRef.current = setInterval(goToNext, 8000);
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isAutoPlaying, activeIndex]);

  const renderStars = (rating: number) => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          className={`w-5 h-5 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
        />
      ));
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <span className="inline-block bg-blue-100 text-blue-600 text-sm font-semibold px-4 py-1 rounded-full mb-4">
            آراء العملاء
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">ماذا يقول عملاؤنا</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            آراء وتقييمات عملائنا الكرام الذين استفادوا من خدماتنا القانونية
          </p>
          <div className="w-20 h-1 bg-blue-600 mx-auto mt-6"></div>
        </div>

        <div className="relative">
          {/* Navigation Arrows */}
          <div className="flex justify-between items-center absolute left-0 right-0 top-1/2 -translate-y-1/2 z-10 px-4">
            <Button
              variant="outline"
              size="icon"
              className="rounded-full w-12 h-12 bg-white shadow-lg hover:bg-gray-100"
              onClick={goToPrev}
            >
              <ChevronRight className="w-6 h-6" />
              <span className="sr-only">السابق</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="rounded-full w-12 h-12 bg-white shadow-lg hover:bg-gray-100"
              onClick={goToNext}
            >
              <ChevronLeft className="w-6 h-6" />
              <span className="sr-only">التالي</span>
            </Button>
          </div>

          {/* Testimonials Slider */}
          <div 
            ref={sliderRef}
            className="relative overflow-hidden"
          >
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${activeIndex * (100 / 3)}%)`,
                width: `${testimonials.length * (100 / 3)}%`
              }}
            >
              {testimonials.map((testimonial, index) => (
                <div 
                  key={testimonial.id}
                  className={`w-full md:w-1/3 px-4 transition-all duration-300 ${activeIndex === index ? 'scale-105' : 'scale-95 opacity-70'}`}
                >
                  <div className="bg-white rounded-xl p-6 shadow-lg h-full">
                    <div className="flex items-center mb-4">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                            <img 
                              src={testimonial.avatar} 
                              alt={testimonial.name} 
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                            <p className="text-sm text-gray-600">{testimonial.role} - {testimonial.company}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="flex">
                          {renderStars(testimonial.rating)}
                        </div>
                      </div>
                    </div>
                    <div className="relative">
                      <Quote className="absolute -top-2 right-0 text-gray-200 w-8 h-8" />
                      <p className="text-gray-700 mb-4 leading-relaxed">{testimonial.content}</p>
                      <div className="text-sm text-gray-500">{testimonial.date}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2 space-x-reverse">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === activeIndex ? 'bg-blue-600 w-8' : 'bg-gray-300'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 md:p-12 text-center text-white">
          <div className="max-w-3xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">هل أنت مستعد لبدء قضيتك القانونية؟</h3>
            <p className="text-blue-100 mb-8 text-lg">
              تواصل معنا اليوم واحصل على استشارة قانونية مجانية من خبرائنا
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button 
                variant="outline" 
                className="bg-white/10 hover:bg-white/20 border-white text-white hover:text-white"
              >
                اتصل بنا الآن
              </Button>
              <Button 
                variant="default" 
                className="bg-white text-blue-700 hover:bg-gray-100"
              >
                احجز استشارة
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
